import './style.css'
import { AuthForm } from './js/components/AuthForm.js'
import './js/utils/apiTest.js' // Auto-test API connection in development

// Initialize the application
class App {
  constructor() {
    this.init();
  }

  init() {
    this.setupDOM();
    this.initializeAuth();
  }

  setupDOM() {
    document.querySelector('#app').innerHTML = `
      <div id="authContainer">
        <!-- Auth forms will be rendered here -->
      </div>
    `;
  }

  initializeAuth() {
    this.authForm = new AuthForm('authContainer');
    this.authForm.checkAuthState();
  }
}

// Start the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new App();
});
